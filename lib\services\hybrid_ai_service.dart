import 'dart:async';
import 'package:flutter/foundation.dart';
import 'gemini_service.dart';
import 'llava_service.dart';

/// Hybrid AI service that uses Gemini for text and LLaVA for vision
class HybridAIService {
  static bool _isInitialized = false;
  
  /// Initialize both services
  static Future<void> initialize({bool forceRefresh = false}) async {
    if (_isInitialized && !forceRefresh) {
      return;
    }
    
    try {
      if (kDebugMode) {
        print('HybridAIService: Initializing...');
      }
      
      // Initialize Gemini service for text
      await GeminiService.initialize(forceRefresh: forceRefresh);
      
      // Initialize LLaVA service for vision
      await LLaVAService.initialize();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('HybridAIService: Initialization complete');
      }
    } catch (error) {
      if (kDebugMode) {
        print('HybridAIService: Initialization failed: $error');
      }
      rethrow;
    }
  }
  
  /// Send a text message using Gemini
  static Future<String> sendMessage(
      String message, 
      List<Map<String, String>> conversationHistory,
      {String? responseLength}) async {
    return await GeminiService.sendMessage(
      message, 
      conversationHistory, 
      responseLength: responseLength
    );
  }
  
  /// Analyze an image using LLaVA
  static Future<String> analyzeImage(String imagePath, String prompt) async {
    try {
      // Use LLaVA for image analysis
      return await LLaVAService.analyzeImage(imagePath, prompt);
    } catch (e) {
      if (kDebugMode) {
        print('HybridAIService: LLaVA failed, falling back to Gemini: $e');
      }
      
      // Fallback to Gemini if LLaVA fails
      try {
        return await GeminiService.analyzeImage(imagePath, prompt);
      } catch (geminiError) {
        if (kDebugMode) {
          print('HybridAIService: Both LLaVA and Gemini failed: $geminiError');
        }
        
        // Return user-friendly error message
        return _getErrorMessage(e, geminiError);
      }
    }
  }
  
  /// Get user-friendly error message
  static String _getErrorMessage(dynamic llavaError, dynamic geminiError) {
    // Try to get a meaningful error message from either service
    String llavaMsg = LLaVAService.getErrorMessage(llavaError);
    String geminiMsg = GeminiService.getErrorMessage(geminiError);
    
    // Return the more specific error message
    if (llavaMsg.contains('network') || llavaMsg.contains('internet')) {
      return llavaMsg;
    } else if (geminiMsg.contains('network') || geminiMsg.contains('internet')) {
      return geminiMsg;
    } else {
      return '''I'm having trouble analyzing images right now. This could be due to:
      
• Network connectivity issues
• Temporary service unavailability
• Image format compatibility

Please try again in a moment, or describe what you see in the image and I'll help you with travel information based on your description!''';
    }
  }
  
  /// Get error message (delegates to appropriate service)
  static String getErrorMessage(dynamic error) {
    return GeminiService.getErrorMessage(error);
  }
  
  /// Model selection methods (delegates to Gemini)
  static Future<void> setSelectedModel(String model) async {
    return await GeminiService.setSelectedModel(model);
  }
  
  static String getSelectedModel() {
    return GeminiService.getSelectedModel();
  }
  
  static bool isPremiumModelSelected() {
    return GeminiService.isPremiumModelSelected();
  }
  
  /// Get available models (delegates to Gemini)
  static String get defaultModel => GeminiService.defaultModel;
  static String get premiumModel => GeminiService.premiumModel;
  
  /// Get last used models for debugging
  static String? getLastUsedTextModel() {
    return GeminiService.getLastUsedTextModel();
  }
  
  static String? getLastUsedVisionModel() {
    // Since we're using LLaVA for vision, return a constant
    return 'llava-hf/llava-1.5-13b-hf';
  }
  
  /// Check if services are initialized
  static bool get isInitialized => _isInitialized;
}
