# LLaVA-1.5-13B Vision Model Integration

This document explains how to set up and use the LLaVA-1.5-13B vision model integration in TripWiseGo.

## Overview

The app now uses a hybrid AI approach:
- **Text Generation**: Gemini/Gemma models (existing functionality)
- **Image Analysis**: LLaVA-1.5-13B model via Hugging Face API

## Setup Instructions

### 1. Get Hugging Face API Key

1. Visit [Hugging Face](https://huggingface.co/)
2. Create an account or sign in
3. Go to [Settings > Access Tokens](https://huggingface.co/settings/tokens)
4. Create a new token with "Read" permissions
5. Copy the token (it starts with `hf_`)

### 2. Configure API Key

1. Open `lib/services/llava_service.dart`
2. Replace `hf_your_api_key_here` with your actual API key:

```dart
static const String _apiKey = 'hf_your_actual_api_key_here';
```

### 3. Model Information

- **Model**: `llava-hf/llava-1.5-13b-hf`
- **Provider**: Hugging Face Inference API
- **Documentation**: https://huggingface.co/llava-hf/llava-1.5-13b-hf

## Features

### Image Analysis Capabilities

The LLaVA model can analyze images for:
- Location identification (landmarks, cities, countries)
- Travel-related content (hotels, restaurants, attractions)
- Cultural and historical significance
- Practical travel recommendations
- Scene description and context

### Fallback System

The hybrid service includes a robust fallback system:
1. **Primary**: LLaVA-1.5-13B for image analysis
2. **Fallback**: Gemini vision models if LLaVA fails
3. **Error Handling**: User-friendly error messages

## Usage

### In Chat Interface

Users can upload images in the Wanderly Chat screen, and the system will:
1. Attempt analysis with LLaVA-1.5-13B
2. Fall back to Gemini if needed
3. Provide travel-focused insights and recommendations

### API Behavior

- **Timeout**: 30 seconds per request
- **Retries**: Up to 3 attempts with exponential backoff
- **Model Loading**: Handles "model loading" responses gracefully

## Error Handling

The system handles various error scenarios:

- **Network Issues**: Clear network connectivity messages
- **Model Loading**: Informative wait time estimates
- **API Limits**: Graceful degradation to fallback models
- **Invalid Images**: Helpful troubleshooting guidance

## Testing

Run the integration tests:

```bash
flutter test test/llava_integration_test.dart
```

## Troubleshooting

### Common Issues

1. **"Model is loading" errors**
   - The model may take 10-20 seconds to start up
   - This is normal for the first request after inactivity

2. **API key errors**
   - Verify your API key is correct
   - Ensure it has "Read" permissions
   - Check that it hasn't expired

3. **Network timeouts**
   - The model may be under heavy load
   - The system will automatically retry and fall back to Gemini

### Debug Information

Enable debug mode to see detailed logs:
- LLaVA request attempts and responses
- Fallback behavior
- Error details

## Performance Considerations

- **First Request**: May take 10-20 seconds (model loading)
- **Subsequent Requests**: Typically 2-5 seconds
- **Image Size**: Automatically resized to 1024x1024 for optimal performance
- **Concurrent Requests**: Limited by Hugging Face API quotas

## API Limits

Hugging Face Inference API has usage limits:
- **Free Tier**: Limited requests per month
- **Pro Tier**: Higher limits available
- **Rate Limiting**: Automatic retry with backoff

## Security Notes

- API keys should be kept secure
- Consider using environment variables in production
- Monitor API usage to avoid unexpected charges

## Future Enhancements

Potential improvements:
- Support for additional vision models
- Caching for repeated image analysis
- Batch processing for multiple images
- Custom model fine-tuning for travel-specific tasks
