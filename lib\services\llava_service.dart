import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../utils/network_helper.dart';

/// LLaVA-1.5-13B vision model service for image analysis
class LLaVAService {
  // Hugging Face API configuration
  static const String _baseUrl =
      'https://api-inference.huggingface.co/models/Qwen/Qwen2.5-VL-7B-Instruct';
  static const String _apiKey = '*************************************';

  static const int _timeoutSeconds = 30;
  static const int _maxRetries = 3;

  /// Initialize the service
  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('LLaVAService: Initializing...');
      }

      // Test API connectivity
      await _testConnection();

      if (kDebugMode) {
        print('LLaVAService: Initialization complete');
      }
    } catch (error) {
      if (kDebugMode) {
        print('LLaVAService: Initialization failed: $error');
      }
      rethrow;
    }
  }

  /// Test API connection
  static Future<void> _testConnection() async {
    try {
      final response = await http.get(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode != 200 && response.statusCode != 503) {
        throw Exception('API connection test failed: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('LLaVAService: Connection test failed: $e');
      }
      // Don't throw here as the model might be loading
    }
  }

  /// Analyze an image with travel-focused prompts
  static Future<String> analyzeImage(String imagePath, String prompt) async {
    // Check network connectivity
    final hasInternet = await NetworkHelper.hasInternetConnection();
    if (!hasInternet) {
      throw Exception(
          'No internet connection available. Please check your network settings and try again.');
    }

    try {
      // Read and encode image file
      final imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        throw Exception('Image file not found: $imagePath');
      }

      final imageBytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(imageBytes);

      // Enhanced travel-focused prompt
      final enhancedPrompt = '''
$prompt

Please provide detailed information about:
- Location identification (landmarks, cities, countries)
- Travel recommendations and tips
- Cultural or historical significance
- Practical travel advice

Be detailed and informative, as this is for travel planning purposes.
''';

      // Attempt request with retries
      return await _attemptRequestWithRetries(enhancedPrompt, base64Image);
    } catch (e) {
      if (kDebugMode) {
        print('LLaVAService: Image analysis failed: $e');
      }

      // Return a helpful error message
      return '''I can see you've shared an image, but I'm currently unable to analyze images directly. However, I'd be happy to help you with travel information if you can describe what you see in the image or tell me the location!

Feel free to ask me about:
- Destination recommendations
- Travel planning advice
- Local attractions and activities
- Cultural insights and tips

What would you like to know about your travel plans?''';
    }
  }

  /// Attempt request with retry logic
  static Future<String> _attemptRequestWithRetries(
      String prompt, String base64Image) async {
    Exception? lastException;

    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      if (kDebugMode) {
        print('LLaVAService: Attempt $attempt/$_maxRetries');
      }

      try {
        final response = await _makeAPIRequest(prompt, base64Image);

        if (kDebugMode) {
          print('LLaVAService: Success on attempt $attempt');
        }

        return response;
      } catch (error) {
        lastException =
            error is Exception ? error : Exception(error.toString());

        if (kDebugMode) {
          print('LLaVAService: Attempt $attempt failed: $error');
        }

        // Wait before retry (exponential backoff)
        if (attempt < _maxRetries) {
          final waitTime = Duration(seconds: attempt * 2);
          await Future.delayed(waitTime);
        }
      }
    }

    // All attempts failed
    throw lastException ?? Exception('All retry attempts failed');
  }

  /// Make API request to Hugging Face
  static Future<String> _makeAPIRequest(
      String prompt, String base64Image) async {
    // For LLaVA model, the input format should be:
    // - image as base64 string
    // - text prompt
    final requestBody = {
      'inputs': {
        'image': base64Image,
        'text': prompt,
      },
      'parameters': {
        'max_new_tokens': 512,
        'temperature': 0.3,
        'do_sample': true,
        'return_full_text': false,
      },
    };

    final response = await http
        .post(
          Uri.parse(_baseUrl),
          headers: {
            'Authorization': 'Bearer $_apiKey',
            'Content-Type': 'application/json',
          },
          body: jsonEncode(requestBody),
        )
        .timeout(const Duration(seconds: _timeoutSeconds));

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);

      // Handle different response formats
      if (responseData is List && responseData.isNotEmpty) {
        final firstResult = responseData[0];
        if (firstResult is Map<String, dynamic> &&
            firstResult.containsKey('generated_text')) {
          return firstResult['generated_text'] as String;
        }
      } else if (responseData is Map<String, dynamic>) {
        if (responseData.containsKey('generated_text')) {
          return responseData['generated_text'] as String;
        } else if (responseData.containsKey('answer')) {
          return responseData['answer'] as String;
        }
      }

      throw Exception('Unexpected response format: $responseData');
    } else if (response.statusCode == 503) {
      // Model is loading
      final responseData = jsonDecode(response.body);
      final estimatedTime = responseData['estimated_time'] ?? 20;
      throw Exception(
          'Model is loading. Please wait approximately $estimatedTime seconds and try again.');
    } else {
      final errorMessage = response.body.isNotEmpty
          ? response.body
          : 'HTTP ${response.statusCode}';
      throw Exception('API request failed: $errorMessage');
    }
  }

  /// Get error message for user display
  static String getErrorMessage(dynamic error) {
    final errorString = error.toString();

    if (errorString.contains('No internet connection')) {
      return 'No internet connection. Please check your network and try again.';
    } else if (errorString.contains('Model is loading')) {
      return 'The AI model is starting up. Please wait a moment and try again.';
    } else if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again.';
    } else if (errorString.contains('Image file not found')) {
      return 'Could not access the image file. Please try uploading again.';
    } else {
      return 'Unable to analyze the image at the moment. Please try again later.';
    }
  }
}
