import 'package:flutter_test/flutter_test.dart';
import 'package:tripwisego/services/llava_service.dart';
import 'package:tripwisego/services/hybrid_ai_service.dart';
import 'dart:io';

void main() {
  group('LLaVA Integration Tests', () {
    test('LLaVA service initialization', () async {
      // Test that the service can be initialized without errors
      expect(() => LLaVAService.initialize(), returnsNormally);
    });

    test('Hybrid AI service initialization', () async {
      // Test that the hybrid service can be initialized
      expect(() => HybridAIService.initialize(), returnsNormally);
    });

    test('Error message formatting', () {
      // Test error message handling
      final networkError = 'No internet connection available';
      final timeoutError = 'Request timeout';
      final modelLoadingError =
          'Model is loading. Please wait approximately 20 seconds';

      expect(LLaVAService.getErrorMessage(networkError), contains('network'));
      expect(LLaVAService.getErrorMessage(timeoutError), contains('timed out'));
      expect(LLaVAService.getErrorMessage(modelLoadingError),
          contains('starting up'));
    });

    test('Image analysis with invalid path', () async {
      // Test handling of invalid image paths
      const invalidPath = '/invalid/path/to/image.jpg';
      const prompt = 'Analyze this image';

      final result = await LLaVAService.analyzeImage(invalidPath, prompt);

      // Should return error message instead of throwing
      expect(result, contains('unable to analyze images directly'));
    });

    test('Hybrid service fallback behavior', () async {
      // Test that hybrid service provides fallback error messages
      const invalidPath = '/invalid/path/to/image.jpg';
      const prompt = 'Analyze this image';

      final result = await HybridAIService.analyzeImage(invalidPath, prompt);

      // Should return a helpful error message instead of throwing
      expect(result, isA<String>());
      expect(result, contains('unable to analyze images directly'));
    });

    test('Model selection delegation', () {
      // Test that hybrid service properly delegates model selection
      expect(HybridAIService.defaultModel, isNotEmpty);
      expect(HybridAIService.premiumModel, isNotEmpty);
      expect(HybridAIService.getSelectedModel(), isNotEmpty);
    });

    test('Vision model tracking', () {
      // Test that vision model tracking returns LLaVA model
      final lastUsedVision = HybridAIService.getLastUsedVisionModel();
      expect(lastUsedVision, equals('llava-hf/llava-1.5-13b-hf'));
    });
  });

  group('API Request Format Tests', () {
    test('Base64 encoding validation', () {
      // Test that base64 encoding works correctly
      const testString = 'Hello, World!';
      final encoded = testString.codeUnits;
      expect(encoded, isNotEmpty);
    });

    test('Request timeout configuration', () {
      // Test that timeout is properly configured
      // This is a compile-time test to ensure constants are defined
      expect(30, greaterThan(0)); // _timeoutSeconds equivalent
      expect(3, greaterThan(0)); // _maxRetries equivalent
    });
  });

  group('Error Handling Tests', () {
    test('Network error handling', () {
      final error = Exception('No internet connection available');
      final message = LLaVAService.getErrorMessage(error);
      expect(message, contains('network'));
    });

    test('Model loading error handling', () {
      final error =
          Exception('Model is loading. Please wait approximately 20 seconds');
      final message = LLaVAService.getErrorMessage(error);
      expect(message, contains('starting up'));
    });

    test('Generic error handling', () {
      final error = Exception('Unknown error occurred');
      final message = LLaVAService.getErrorMessage(error);
      expect(message, contains('try again later'));
    });
  });
}
